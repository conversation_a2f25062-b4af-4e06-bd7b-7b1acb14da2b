import 'dart:async';
import 'package:aslaa/components/status_display.dart';
import 'package:aslaa/flutter_flow/flutter_flow_icon_button.dart';
import 'package:aslaa/utils/status_utils.dart';
import 'package:flutter/material.dart';

import 'package:audioplayers/audioplayers.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as google_maps;
import 'package:provider/provider.dart';
// FlutterFlow and custom imports
import 'package:aslaa/flutter_flow/flutter_flow_google_map.dart'
    as ff_google_map;
import 'package:aslaa/flutter_flow/flutter_flow_theme.dart';
import 'package:aslaa/flutter_flow/internationalization.dart';

import 'package:aslaa/models/device.dart';
import 'package:aslaa/models/user.dart';
import 'package:aslaa/mqtt/mqtt_websocket.dart';
import 'package:aslaa/providers/app_provider.dart';

import 'package:aslaa/service/device_command_service.dart';
import 'package:aslaa/utils/ApiCallLimiter.dart';
import 'package:aslaa/utils/helper_functions.dart';
import 'package:aslaa/widgets/car_display_widget.dart';
import 'package:aslaa/widgets/custom_app_bar.dart';
import 'package:aslaa/widgets/map_display_widget.dart';
import 'package:aslaa/widgets/right_side_icons.dart';
import 'package:aslaa/widgets/left_side_icons.dart';
import 'package:aslaa/components/scrollable_icon_button.dart';
import 'package:aslaa/components/scrollable_map_button.dart';

class MainWidget extends StatefulWidget {
  const MainWidget({Key? key}) : super(key: key);

  @override
  _MainWidgetState createState() => _MainWidgetState();
}

class _MainWidgetState extends State<MainWidget>
    with
        TickerProviderStateMixin,
        AutomaticKeepAliveClientMixin,
        WidgetsBindingObserver {
  @override
  bool get wantKeepAlive => true;

  late AnimationController _countdownAnimationController;
  MqttHandler? mqttHandler;
  ff_google_map.LatLng? googleMapsCenter;
  final googleMapsController = Completer<ff_google_map.GoogleMapController>();
  bool showMap = false;
  google_maps.BitmapDescriptor? carIcon;
  bool unlock = false;
  bool loading = false;
  final player = AudioPlayer();
  bool existDevice = false;
  bool isRecording = false;
  late ApiCallLimiter _apiCallLimiter;
  DeviceCommandService? _deviceCommandService;
  late ScaffoldMessengerState scaffoldMessenger;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    HelperFunctions.createCarIcon().then((icon) {
      setState(() {
        carIcon = icon;
      });
    });

    final appProvider = Provider.of<AppProvider>(context, listen: false);
    appProvider.fetchSimStatus();

    _apiCallLimiter = ApiCallLimiter();
    _apiCallLimiter.initialize();

    _countdownAnimationController = AnimationController(
      vsync: this,
      duration: Duration(seconds: 20),
    )..addStatusListener((status) {
        if (status == AnimationStatus.completed && mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Time out',
                  style: TextStyle(fontFamily: 'PorscheNumber')),
              duration: Duration(seconds: 3),
            ),
          );
        }
      });

    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {
        mqttHandler =
            Provider.of<AppProvider>(context, listen: false).mqttHandler;

        if (mqttHandler != null) {
          _deviceCommandService = DeviceCommandService(
            mqttHandler: mqttHandler!,
            apiCallLimiter: _apiCallLimiter,
            context: context,
          );
        }

        // Send check command when the page is accessed
        final user =
            Provider.of<AppProvider>(context, listen: false).authClient;
        if (user != null && _deviceCommandService != null) {
          _deviceCommandService!.sendDeviceCommand(user, 'check');
        }
      });

      // Fetch weather data with a valid location
      appProvider.fetchWeatherData(
          'Ulaanbaatar'); // Replace 'New York' with the desired location
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    scaffoldMessenger = ScaffoldMessenger.of(context);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    player.dispose();
    _countdownAnimationController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state); // Call the super method

    final appProvider = Provider.of<AppProvider>(context, listen: false);

    switch (state) {
      case AppLifecycleState.resumed:
        appProvider.handleAppResumed();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
        appProvider.handleAppPaused();
        break;
      default:
        break;
    }
  }

  Future<void> onUnlock(User user) async {
    if (user.status == 'expired') {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
              'License has expired. Please renew to use this feature.',
              style: TextStyle(fontFamily: 're')),
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    if (user.device != null && _deviceCommandService != null) {
      bool result = await _deviceCommandService!
          .sendDeviceCommand(user, unlock == true ? 'lock' : 'unlock');

      if (result) {
        setState(() {
          unlock = !unlock;
        });
        await player.play(AssetSource('audios/lock.mp3'));
      } else {
        print('Failed to send command');
      }
    }
  }

  Future<void> _startRecording() async {
    final user = Provider.of<AppProvider>(context, listen: false).authClient;
    if (user?.status == 'expired') {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
              'License has expired. Please renew to use this feature.',
              style: TextStyle(fontFamily: 'PorscheNumber')),
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }
  }

  Future<bool> sendDeviceCommand(User user, String command) async {
    if (user.status == 'expired') {
      scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(
              'License has expired. Please renew to use this feature.',
              style: TextStyle(fontFamily: 'PorscheNumber')),
          duration: Duration(seconds: 3),
        ),
      );
      return false;
    }

    if (command == 'as') {
      // Start the countdown animation
      _countdownAnimationController.reset();
      _countdownAnimationController.forward();
      print('Countdown animation started');
    }

    if (mqttHandler != null && mqttHandler!.isConnected) {
      try {
        // Attempt to send the command via MQTT
        bool mqttResult = await mqttHandler!.sendDeviceCommand(user, command);
        return mqttResult;
      } catch (e) {
        print('MQTT_LOGS:: Error sending via MQTT: $e');
        return false; // Indicate failure if MQTT send fails
      }
    } else {
      print('MQTT_LOGS:: MQTT is not connected.');
      return false; // Indicate failure if MQTT is not connected
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Consumer<AppProvider>(
      builder: (context, appProvider, child) {
        final DeviceStatus? ds = appProvider.ds;

        print('Building MainWidget with ds: ${ds?.toJson()}');

        if (ds == null || appProvider.mqttHandler == null) {
          return Center(child: CircularProgressIndicator());
        }

        final simBalance = appProvider.simBalance;
        final simExpiredDate = appProvider.simExpiredDate;
        final user = appProvider.authClient;
        final mqttHandler = appProvider.mqttHandler;

        if (user == null || user.device == null) {
          return Center(
              child: Text('No device connected.',
                  style: TextStyle(fontFamily: 'PorscheNumber')));
        }

        final Device device = user.device!;
        final String uix = device.uix;

        final statusProps = StatusUtils.getStatusProperties(user.status);

        return Scaffold(
          backgroundColor: FlutterFlowTheme.of(context).primaryBackground,
          appBar: CustomAppBar(
            title: FFLocalizations.of(context).getText(
              'qwth9wth' /* REMOTE CAR CONTROL SYSTEM */,
            ),
          ),
          body: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                // Status Indicators and Car Display
                Container(
                  height: MediaQuery.of(context).size.height * 0.5,
                  child: Stack(
                    children: [
                      // Car display in the background
                      CarDisplayWidget(
                        ds: ds,
                        loading: loading,
                        uix: uix,
                        countdownController: _countdownAnimationController,
                        key: widget.key,
                      ),

                      // Left side indicators with pointer events enabled
                      Positioned(
                        left: 16.0,
                        top: 0,
                        bottom: 0,
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: StatusIndicatorsWidget(
                            ds: ds,
                            rssi: ds.rssi.toInt(),
                            simBalance: simBalance,
                            simExpiredDate: simExpiredDate,
                            onSimCardTap: () {
                              debugPrint('SIM card icon tapped');

                              // Get the user from the app provider
                              final user = Provider.of<AppProvider>(context,
                                      listen: false)
                                  .authClient;

                              if (user != null) {
                                // Send the "sim" command via MQTT
                                if (mqttHandler != null &&
                                    mqttHandler!.isConnected) {
                                  mqttHandler!.sendDeviceCommand(user, 'sim');
                                  debugPrint('MQTT "sim" command sent');
                                } else {
                                  debugPrint('MQTT handler is not connected');
                                }

                                // Also check the SIM card status via API
                                String? deviceNumber =
                                    user.device?.deviceNumber;
                                if (deviceNumber != null) {
                                  Provider.of<AppProvider>(context,
                                          listen: false)
                                      .checkSimCard(deviceNumber);
                                  debugPrint('API SIM card check initiated');
                                } else {
                                  debugPrint("Device number is not available");
                                }
                              }
                            },
                          ),
                        ),
                      ),

                      // Right Side Buttons
                      RightSideButtons(
                        isRecording: isRecording,
                        onRecordingPressed: () async {
                          final user =
                              Provider.of<AppProvider>(context, listen: false)
                                  .authClient;
                          if (user?.status != 'expired') {
                            await _startRecording();
                          } else {
                            scaffoldMessenger.showSnackBar(
                              SnackBar(
                                content: Text(
                                    'License has expired. Please renew to use this feature.',
                                    style:
                                        TextStyle(fontFamily: 'PorscheNumber')),
                                duration: Duration(seconds: 3),
                              ),
                            );
                          }
                        },
                      ),
                    ],
                  ),
                ),

                // Status Display
                StatusDisplay(
                  userStatus: statusProps['text'] ?? 'Unknown',
                  userStatusColor: statusProps['color'] ?? Colors.grey,
                  speed: ds.Speed.toDouble(),
                  speedUnit: FFLocalizations.of(context).getText('kmh'),
                  device: device,
                  isConnected: mqttHandler!.isConnected,
                ),

                // Bottom Buttons
                Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Divider(
                      thickness: 1,
                    ),
                    if (!loading)
                      Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(24, 24, 24, 0),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            if (((uix.toLowerCase().contains('car') ||
                                uix.toLowerCase().contains('moped'))))
                              FlutterFlowIconButton(
                                borderColor: unlock
                                    ? FlutterFlowTheme.of(context).tertiaryColor
                                    : FlutterFlowTheme.of(context)
                                        .secondaryColor,
                                borderRadius: 40,
                                borderWidth: 1,
                                buttonSize: 80,
                                fillColor: unlock
                                    ? Color(0x6DEE8B60)
                                    : Color(0x6939D2C0),
                                icon: Icon(
                                  !unlock
                                      ? Icons.lock_open
                                      : Icons.lock_outline,
                                  color:
                                      FlutterFlowTheme.of(context).primaryText,
                                  size: 30,
                                ),
                                onPressed: () async {
                                  await onUnlock(user);
                                },
                              ),
                            ScrollableIconButton(
                              user: user,
                              sendDeviceCommand: sendDeviceCommand,
                              deviceStatus: ds,
                              mqtthandler: mqttHandler,
                            ),
                            ScrollableMapButton(
                              isRecording: isRecording,
                              onPressed: () async {
                                if (user.status == 'expired') {
                                  scaffoldMessenger.showSnackBar(
                                    SnackBar(
                                      content: Text(
                                          'License has expired. Please renew to use this feature.',
                                          style: TextStyle(
                                              fontFamily: 'PorscheNumber')),
                                      duration: Duration(seconds: 3),
                                    ),
                                  );
                                  return;
                                }
                                if ((ds.Lat) > 0 && (ds.Lon) > 0) {
                                  setState(() {
                                    showMap = true;
                                  });
                                }
                                await HelperFunctions.sendMqttCommand(
                                    mqttHandler, user, 'check');
                                print('map button pressed...');
                              },
                            ),
                          ],
                        ),
                      ),
                    // Map Display
                    MapDisplayWidget(
                      showMap: showMap,
                      ds: ds,
                      googleMapsController: googleMapsController,
                      googleMapsCenter: googleMapsCenter,
                      carIcon: carIcon,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
