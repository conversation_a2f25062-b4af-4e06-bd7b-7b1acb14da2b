// lib/components/speed_indicator.dart

import 'package:flutter/material.dart';

class SpeedIndicator extends StatelessWidget {
  final double speed;
  final String speedUnit;
  final String label;
  final IconData icon;

  const SpeedIndicator({
    Key? key,
    required this.speed,
    this.speedUnit = 'km/h',
    this.label = 'Speed',
    this.icon = Icons.speed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsetsDirectional.fromSTEB(0, 0, 0, 12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            width: 24,
            height: 24,
            child: Icon(
              icon,
              color: _getSpeedColor(),
              size: 18,
            ),
          ),
          SizedBox(width: 8),
          Expanded(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(
                begin: speed - 1,
                end: speed,
              ),
              duration: Duration(seconds: 1),
              builder: (context, value, child) {
                return Text(
                  '${value.toStringAsFixed(0)} $speedUnit',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontFamily: 'PorscheNumber',
                        fontSize: 18,
                        color: _getSpeedColor(),
                        fontWeight: FontWeight.bold,
                      ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Color _getSpeedColor() {
    if (speed == 0) {
      return Colors.grey;
    } else if (speed <= 30) {
      return Colors.green;
    } else if (speed <= 60) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
